"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
function activate(context) {
    console.log('Augment Monitor is now active!');
    const monitor = new AugmentMonitor(context);
    // Register commands
    const startCommand = vscode.commands.registerCommand('augment-monitor.start', () => {
        monitor.start();
        vscode.window.showInformationMessage('Augment Monitor started');
    });
    const stopCommand = vscode.commands.registerCommand('augment-monitor.stop', () => {
        monitor.stop();
        vscode.window.showInformationMessage('Augment Monitor stopped');
    });
    const configureCommand = vscode.commands.registerCommand('augment-monitor.configure', () => {
        monitor.configure();
    });
    context.subscriptions.push(startCommand, stopCommand, configureCommand, monitor);
    // Auto-start if enabled in settings
    const config = vscode.workspace.getConfiguration('augmentMonitor');
    if (config.get('enabled')) {
        monitor.start();
    }
}
exports.activate = activate;
class AugmentMonitor {
    constructor(context) {
        this.context = context;
        this.intervalId = null;
        this.lastOutputContent = '';
        this.disposables = [];
        this.lastTriggerTimes = new Map();
        this.documentChangeListener = null;
        this.webviewPanels = new Set();
        this.setupEventListeners();
    }
    setupEventListeners() {
        // 监控文档变化
        this.documentChangeListener = vscode.workspace.onDidChangeTextDocument((event) => {
            this.handleDocumentChange(event);
        });
        this.disposables.push(this.documentChangeListener);
        // 监控活动编辑器变化
        const activeEditorListener = vscode.window.onDidChangeActiveTextEditor((editor) => {
            if (editor) {
                this.handleActiveEditorChange(editor);
            }
        });
        this.disposables.push(activeEditorListener);
        // 监控窗口状态变化
        const windowStateListener = vscode.window.onDidChangeWindowState((state) => {
            if (state.focused) {
                this.handleWindowFocusChange();
            }
        });
        this.disposables.push(windowStateListener);
        // 监控webview创建
        const webviewListener = vscode.window.registerWebviewPanelSerializer('augment-chat', {
            deserializeWebviewPanel: async (panel, state) => {
                this.monitorWebviewPanel(panel);
                return Promise.resolve();
            }
        });
        this.disposables.push(webviewListener);
    }
    handleDocumentChange(event) {
        // 检查是否是我们感兴趣的文档变化
        const document = event.document;
        // 跳过太频繁的变化
        if (event.contentChanges.length === 0)
            return;
        // 检查文档内容是否包含Augment相关内容
        const content = document.getText();
        if (this.looksLikeAugmentContent(content)) {
            this.processContentChange(content, `document:${document.fileName}`);
        }
    }
    handleActiveEditorChange(editor) {
        const content = editor.document.getText();
        if (this.looksLikeAugmentContent(content)) {
            this.processContentChange(content, `editor:${editor.document.fileName}`);
        }
    }
    handleWindowFocusChange() {
        // 当窗口获得焦点时，检查剪贴板内容
        this.checkClipboardContent();
    }
    monitorWebviewPanel(panel) {
        this.webviewPanels.add(panel);
        // 监听webview消息
        panel.webview.onDidReceiveMessage((message) => {
            this.handleWebviewMessage(message, panel);
        });
        // 清理
        panel.onDidDispose(() => {
            this.webviewPanels.delete(panel);
        });
    }
    handleWebviewMessage(message, panel) {
        // 处理来自webview的消息，这可能包含Augment的输出
        if (message && typeof message === 'object') {
            const content = JSON.stringify(message);
            if (this.looksLikeAugmentContent(content)) {
                this.processContentChange(content, `webview:${panel.title}`);
            }
        }
    }
    start() {
        if (this.intervalId) {
            return; // Already running
        }
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const interval = config.get('checkInterval', 2000);
        // 启动定期检查（作为备用机制）
        this.intervalId = setInterval(() => {
            this.periodicCheck();
        }, interval);
        console.log('Augment Monitor started with interval:', interval);
    }
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('Augment Monitor stopped');
        }
    }
    async periodicCheck() {
        try {
            // 定期检查剪贴板
            await this.checkClipboardContent();
            // 检查活动终端
            await this.checkActiveTerminal();
            // 检查最近打开的文档
            await this.checkRecentDocuments();
        }
        catch (error) {
            console.error('Error in periodic check:', error);
        }
    }
    async checkClipboardContent() {
        try {
            const clipboardContent = await vscode.env.clipboard.readText();
            if (clipboardContent && clipboardContent !== this.lastOutputContent) {
                if (this.looksLikeAugmentContent(clipboardContent)) {
                    this.processContentChange(clipboardContent, 'clipboard');
                }
            }
        }
        catch (error) {
            console.error('Error checking clipboard:', error);
        }
    }
    async checkActiveTerminal() {
        const activeTerminal = vscode.window.activeTerminal;
        if (activeTerminal) {
            // 虽然不能直接读取终端内容，但可以检查终端名称
            if (activeTerminal.name.toLowerCase().includes('augment')) {
                console.log('Detected Augment terminal activity');
                // 可以在这里触发一些检查逻辑
            }
        }
    }
    async checkRecentDocuments() {
        // 检查最近修改的文档
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            const content = activeEditor.document.getText();
            if (this.looksLikeAugmentContent(content) && content !== this.lastOutputContent) {
                this.processContentChange(content, `document:${activeEditor.document.fileName}`);
            }
        }
    }
    async processContentChange(content, source) {
        // 避免重复处理相同内容
        if (content === this.lastOutputContent)
            return;
        this.lastOutputContent = content;
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get('conditions', []);
        console.log(`Processing content from ${source}, length: ${content.length}`);
        await this.processConditions(content, conditions, source);
    }
    async processConditions(content, conditions, source) {
        for (const condition of conditions) {
            if (!condition.enabled)
                continue;
            // 检查冷却时间
            const conditionKey = `${condition.pattern}-${source}`;
            const lastTrigger = this.lastTriggerTimes.get(conditionKey) || 0;
            const cooldown = condition.cooldown || 5000; // 默认5秒冷却
            if (Date.now() - lastTrigger < cooldown) {
                continue;
            }
            // 检查模式匹配
            const flags = condition.caseSensitive ? 'g' : 'gi';
            try {
                const regex = new RegExp(condition.pattern, flags);
                if (regex.test(content)) {
                    console.log(`Condition matched: ${condition.pattern} in ${source}`);
                    // 记录触发时间
                    this.lastTriggerTimes.set(conditionKey, Date.now());
                    // 发送响应
                    await this.sendAutoResponse(condition.response, source);
                    // 只响应第一个匹配的条件
                    break;
                }
            }
            catch (error) {
                console.error(`Invalid regex pattern: ${condition.pattern}`, error);
            }
        }
    }
    looksLikeAugmentContent(content) {
        if (!content || content.trim().length < 20)
            return false;
        const lowerContent = content.toLowerCase();
        // Augment特征词汇
        const augmentIndicators = [
            'augment',
            'claude',
            'assistant',
            'ai response',
            'function_calls',
            'antml:invoke',
            'antml:function_calls',
            '我来帮你',
            '我可以帮助',
            'codebase-retrieval',
            'str-replace-editor',
            'launch-process',
            'web-search'
        ];
        // 代码生成特征
        const codeIndicators = [
            '```',
            'function',
            'class',
            'import',
            'export',
            'const ',
            'let ',
            'var ',
            'def ',
            'public class'
        ];
        // 错误和问题特征
        const problemIndicators = [
            'error',
            'exception',
            'failed',
            '错误',
            '失败',
            '问题',
            'bug',
            'issue'
        ];
        // 检查是否包含Augment特征
        const hasAugmentFeatures = augmentIndicators.some(indicator => lowerContent.includes(indicator));
        // 检查是否包含代码特征
        const hasCodeFeatures = codeIndicators.some(indicator => lowerContent.includes(indicator));
        // 检查是否包含问题特征
        const hasProblemFeatures = problemIndicators.some(indicator => lowerContent.includes(indicator));
        // 检查长度和结构
        const hasReasonableLength = content.length > 50 && content.length < 100000;
        const hasMultipleLines = content.split('\n').length > 2;
        return (hasAugmentFeatures ||
            (hasCodeFeatures && hasReasonableLength && hasMultipleLines) ||
            (hasProblemFeatures && hasReasonableLength)) &&
            hasReasonableLength;
    }
    async sendAutoResponse(response, source) {
        try {
            console.log(`Sending auto-response for ${source}: ${response}`);
            // 方法1: 尝试发送到活动终端
            const activeTerminal = vscode.window.activeTerminal;
            if (activeTerminal) {
                activeTerminal.sendText(response);
                vscode.window.showInformationMessage(`Auto-response sent to terminal: ${response.substring(0, 50)}...`);
                return;
            }
            // 方法2: 尝试插入到活动编辑器
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                const position = activeEditor.selection.active;
                const success = await activeEditor.edit(editBuilder => {
                    editBuilder.insert(position, response + '\n');
                });
                if (success) {
                    vscode.window.showInformationMessage(`Auto-response inserted: ${response.substring(0, 50)}...`);
                    return;
                }
            }
            // 方法3: 复制到剪贴板并通知
            await vscode.env.clipboard.writeText(response);
            const action = await vscode.window.showInformationMessage(`Auto-response ready: ${response.substring(0, 50)}...`, 'Paste Now', 'Show Full Response', 'Dismiss');
            if (action === 'Paste Now') {
                // 尝试模拟粘贴操作
                await vscode.commands.executeCommand('editor.action.clipboardPasteAction');
            }
            else if (action === 'Show Full Response') {
                // 在新文档中显示完整响应
                const doc = await vscode.workspace.openTextDocument({
                    content: response,
                    language: 'plaintext'
                });
                await vscode.window.showTextDocument(doc);
            }
        }
        catch (error) {
            console.error('Error sending auto-response:', error);
            vscode.window.showErrorMessage(`Failed to send auto-response: ${error}`);
        }
    }
    async configure() {
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get('conditions', []);
        const options = [
            'Add new condition',
            'Edit existing conditions',
            'Toggle monitoring',
            'Set check interval'
        ];
        const selection = await vscode.window.showQuickPick(options, {
            placeHolder: 'Choose configuration option'
        });
        switch (selection) {
            case 'Add new condition':
                await this.addNewCondition();
                break;
            case 'Edit existing conditions':
                await this.editConditions();
                break;
            case 'Toggle monitoring':
                await this.toggleMonitoring();
                break;
            case 'Set check interval':
                await this.setCheckInterval();
                break;
        }
    }
    async addNewCondition() {
        const pattern = await vscode.window.showInputBox({
            prompt: 'Enter regex pattern to match',
            placeHolder: 'e.g., error|failed|exception'
        });
        if (!pattern)
            return;
        const response = await vscode.window.showInputBox({
            prompt: 'Enter response to send when pattern matches',
            placeHolder: 'e.g., Please help me fix this error'
        });
        if (!response)
            return;
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get('conditions', []);
        conditions.push({
            pattern,
            response,
            enabled: true
        });
        await config.update('conditions', conditions, vscode.ConfigurationTarget.Global);
        vscode.window.showInformationMessage('New condition added successfully');
    }
    async editConditions() {
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get('conditions', []);
        if (conditions.length === 0) {
            vscode.window.showInformationMessage('No conditions configured');
            return;
        }
        const items = conditions.map((condition, index) => ({
            label: condition.pattern,
            description: condition.response,
            detail: condition.enabled ? 'Enabled' : 'Disabled',
            index
        }));
        const selection = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select condition to edit'
        });
        if (selection) {
            // Toggle enabled state for now - could be expanded to full editing
            conditions[selection.index].enabled = !conditions[selection.index].enabled;
            await config.update('conditions', conditions, vscode.ConfigurationTarget.Global);
            vscode.window.showInformationMessage(`Condition ${conditions[selection.index].enabled ? 'enabled' : 'disabled'}`);
        }
    }
    async toggleMonitoring() {
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const enabled = config.get('enabled', false);
        await config.update('enabled', !enabled, vscode.ConfigurationTarget.Global);
        if (!enabled) {
            this.start();
        }
        else {
            this.stop();
        }
        vscode.window.showInformationMessage(`Monitoring ${!enabled ? 'enabled' : 'disabled'}`);
    }
    async setCheckInterval() {
        const input = await vscode.window.showInputBox({
            prompt: 'Enter check interval in milliseconds',
            placeHolder: '1000',
            validateInput: (value) => {
                const num = parseInt(value);
                if (isNaN(num) || num < 100) {
                    return 'Please enter a valid number >= 100';
                }
                return null;
            }
        });
        if (input) {
            const config = vscode.workspace.getConfiguration('augmentMonitor');
            await config.update('checkInterval', parseInt(input), vscode.ConfigurationTarget.Global);
            vscode.window.showInformationMessage(`Check interval set to ${input}ms`);
            // Restart monitoring with new interval
            if (this.intervalId) {
                this.stop();
                this.start();
            }
        }
    }
    dispose() {
        this.stop();
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
        this.webviewPanels.clear();
        console.log('Augment Monitor disposed');
    }
}
function deactivate() {
    console.log('Augment Monitor deactivated');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map