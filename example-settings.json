{"augmentMonitor.enabled": true, "augmentMonitor.checkInterval": 2000, "augmentMonitor.conditions": [{"pattern": "error|failed|exception|错误|失败|bug", "response": "请帮我修复这个错误，需要详细的解决方案和步骤", "enabled": true, "caseSensitive": false, "cooldown": 5000}, {"pattern": "完成|complete|done|finished|success", "response": "谢谢！请继续下一步操作", "enabled": true, "caseSensitive": false, "cooldown": 3000}, {"pattern": "需要更多信息|need more info|clarification needed|unclear", "response": "我来提供更多详细信息和上下文背景", "enabled": true, "caseSensitive": false, "cooldown": 5000}, {"pattern": "测试|test|testing|unit test", "response": "请帮我编写相应的测试用例和测试代码", "enabled": true, "caseSensitive": false, "cooldown": 10000}, {"pattern": "优化|optimize|performance|slow", "response": "请帮我优化这段代码的性能", "enabled": true, "caseSensitive": false, "cooldown": 8000}, {"pattern": "文档|documentation|comment|注释", "response": "请帮我添加详细的文档和注释", "enabled": true, "caseSensitive": false, "cooldown": 6000}, {"pattern": "部署|deploy|deployment|发布", "response": "请提供部署相关的指导和最佳实践", "enabled": false, "caseSensitive": false, "cooldown": 15000}, {"pattern": "安全|security|vulnerability|漏洞", "response": "请检查安全问题并提供修复建议", "enabled": true, "caseSensitive": false, "cooldown": 10000}, {"pattern": "重构|refactor|clean up|整理", "response": "请帮我重构这段代码，使其更清晰易读", "enabled": true, "caseSensitive": false, "cooldown": 12000}, {"pattern": "API|接口|interface|endpoint", "response": "请提供API设计建议和最佳实践", "enabled": true, "caseSensitive": false, "cooldown": 8000}]}