"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentMonitor = void 0;
const vscode = require("vscode");
class ContentMonitor {
    constructor() {
        this.lastTriggerTimes = new Map();
        this.fileWatchers = [];
        this.lastClipboardContent = '';
        this.lastEditorContent = '';
        this.onContentDetected = () => { };
        this.setupFileWatchers();
    }
    async checkAllSources() {
        const results = [];
        // 检查剪贴板
        const clipboardResult = await this.checkClipboard();
        if (clipboardResult)
            results.push(clipboardResult);
        // 检查活动编辑器
        const editorResult = this.checkActiveEditor();
        if (editorResult)
            results.push(editorResult);
        // 检查最近的文件变化
        const fileResults = await this.checkRecentFileChanges();
        results.push(...fileResults);
        return results;
    }
    async checkClipboard() {
        try {
            const content = await vscode.env.clipboard.readText();
            if (content && content !== this.lastClipboardContent && content.trim().length > 0) {
                this.lastClipboardContent = content;
                // 检查是否看起来像Augment内容
                if (this.looksLikeAugmentContent(content)) {
                    return {
                        content,
                        source: 'clipboard',
                        timestamp: new Date()
                    };
                }
            }
        }
        catch (error) {
            console.error('Error checking clipboard:', error);
        }
        return null;
    }
    checkActiveEditor() {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor)
            return null;
        let content = '';
        const selection = activeEditor.selection;
        if (!selection.isEmpty) {
            // 如果有选中内容，检查选中的内容
            content = activeEditor.document.getText(selection);
        }
        else {
            // 否则检查最近添加的内容（最后几行）
            const document = activeEditor.document;
            const lineCount = document.lineCount;
            const startLine = Math.max(0, lineCount - 5);
            const range = new vscode.Range(startLine, 0, lineCount - 1, document.lineAt(lineCount - 1).text.length);
            content = document.getText(range);
        }
        if (content && content !== this.lastEditorContent && content.trim().length > 0) {
            this.lastEditorContent = content;
            if (this.looksLikeAugmentContent(content)) {
                return {
                    content,
                    source: 'editor',
                    timestamp: new Date()
                };
            }
        }
        return null;
    }
    async checkRecentFileChanges() {
        // 这个方法会在文件监听器中被调用
        // 这里返回空数组，实际的文件变化检测在setupFileWatchers中处理
        return [];
    }
    setupFileWatchers() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders)
            return;
        // 清理现有的监听器
        this.fileWatchers.forEach(watcher => watcher.dispose());
        this.fileWatchers = [];
        const patterns = [
            '**/*.md',
            '**/*.log',
            '**/.augment/**/*',
            '**/augment-*',
            '**/*augment*',
        ];
        patterns.forEach(pattern => {
            const watcher = vscode.workspace.createFileSystemWatcher(pattern);
            const handleFileChange = async (uri) => {
                try {
                    // 避免检查太大的文件
                    const stat = await vscode.workspace.fs.stat(uri);
                    if (stat.size > 1024 * 1024)
                        return; // 跳过大于1MB的文件
                    const document = await vscode.workspace.openTextDocument(uri);
                    const content = document.getText();
                    if (this.looksLikeAugmentContent(content)) {
                        // 触发内容处理
                        this.onContentDetected({
                            content,
                            source: `file:${uri.fsPath}`,
                            timestamp: new Date()
                        });
                    }
                }
                catch (error) {
                    console.error('Error handling file change:', error);
                }
            };
            watcher.onDidChange(handleFileChange);
            watcher.onDidCreate(handleFileChange);
            this.fileWatchers.push(watcher);
        });
    }
    setContentDetectedCallback(callback) {
        this.onContentDetected = callback;
    }
    looksLikeAugmentContent(content) {
        const lowerContent = content.toLowerCase();
        // Augment特征词汇
        const augmentIndicators = [
            'augment',
            'claude',
            'assistant',
            'ai response',
            'function_calls',
            'antml:invoke',
            'antml:function_calls',
            '我来帮你',
            '我可以帮助',
            'codebase-retrieval',
            'str-replace-editor'
        ];
        // 代码特征
        const codeIndicators = [
            '```',
            'function',
            'class',
            'import',
            'export',
            'const ',
            'let ',
            'var '
        ];
        // 检查是否包含Augment特征
        const hasAugmentFeatures = augmentIndicators.some(indicator => lowerContent.includes(indicator));
        // 检查是否包含代码特征（可能是生成的代码）
        const hasCodeFeatures = codeIndicators.some(indicator => lowerContent.includes(indicator));
        // 检查长度和结构
        const hasReasonableLength = content.length > 50 && content.length < 50000;
        const hasMultipleLines = content.split('\n').length > 3;
        return hasAugmentFeatures || (hasCodeFeatures && hasReasonableLength && hasMultipleLines);
    }
    async processConditions(results, conditions) {
        for (const result of results) {
            for (const condition of conditions) {
                if (!condition.enabled)
                    continue;
                // 检查冷却时间
                const conditionKey = `${condition.pattern}-${result.source}`;
                const lastTrigger = this.lastTriggerTimes.get(conditionKey) || 0;
                const cooldown = condition.cooldown || 5000; // 默认5秒冷却
                if (Date.now() - lastTrigger < cooldown) {
                    continue;
                }
                // 检查模式匹配
                const flags = condition.caseSensitive ? 'g' : 'gi';
                const regex = new RegExp(condition.pattern, flags);
                if (regex.test(result.content)) {
                    console.log(`Condition matched: ${condition.pattern} in ${result.source}`);
                    // 记录触发时间
                    this.lastTriggerTimes.set(conditionKey, Date.now());
                    // 发送响应
                    await this.sendResponse(condition.response, result);
                    // 只响应第一个匹配的条件
                    break;
                }
            }
        }
    }
    async sendResponse(response, context) {
        try {
            // 方法1: 尝试发送到活动终端
            const activeTerminal = vscode.window.activeTerminal;
            if (activeTerminal) {
                activeTerminal.sendText(response);
                vscode.window.showInformationMessage(`Auto-response sent to terminal: ${response.substring(0, 50)}...`);
                return;
            }
            // 方法2: 复制到剪贴板并通知
            await vscode.env.clipboard.writeText(response);
            const action = await vscode.window.showInformationMessage(`Auto-response ready: ${response.substring(0, 50)}...`, 'Paste to Augment', 'Dismiss');
            if (action === 'Paste to Augment') {
                // 尝试粘贴到活动编辑器
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor) {
                    const position = activeEditor.selection.active;
                    await activeEditor.edit(editBuilder => {
                        editBuilder.insert(position, response);
                    });
                }
            }
        }
        catch (error) {
            console.error('Error sending response:', error);
            vscode.window.showErrorMessage(`Failed to send auto-response: ${error}`);
        }
    }
    dispose() {
        this.fileWatchers.forEach(watcher => watcher.dispose());
        this.fileWatchers = [];
    }
}
exports.ContentMonitor = ContentMonitor;
//# sourceMappingURL=contentMonitor.js.map