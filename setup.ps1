# Augment Monitor VSCode Extension Setup Script
# 用于快速设置和测试Augment Monitor插件

Write-Host "=== Augment Monitor Setup ===" -ForegroundColor Green

# 检查Node.js和npm
Write-Host "检查Node.js和npm..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
    Write-Host "npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 请先安装Node.js和npm" -ForegroundColor Red
    exit 1
}

# 安装依赖
Write-Host "安装依赖..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 依赖安装失败" -ForegroundColor Red
    exit 1
}

# 编译TypeScript
Write-Host "编译TypeScript..." -ForegroundColor Yellow
npm run compile

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: TypeScript编译失败" -ForegroundColor Red
    exit 1
}

# 检查编译输出
if (Test-Path "out/extension.js") {
    Write-Host "编译成功！" -ForegroundColor Green
} else {
    Write-Host "错误: 编译输出文件不存在" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 设置完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Cyan
Write-Host "1. 在VSCode中打开此项目文件夹" -ForegroundColor White
Write-Host "2. 按F5启动调试模式" -ForegroundColor White
Write-Host "3. 在新的VSCode窗口中测试插件" -ForegroundColor White
Write-Host ""
Write-Host "插件命令:" -ForegroundColor Cyan
Write-Host "- Ctrl+Shift+P -> 'Start Augment Monitor'" -ForegroundColor White
Write-Host "- Ctrl+Shift+P -> 'Configure Augment Monitor'" -ForegroundColor White
Write-Host "- Ctrl+Shift+P -> 'Stop Augment Monitor'" -ForegroundColor White
Write-Host ""
Write-Host "配置文件示例: example-settings.json" -ForegroundColor Cyan
Write-Host ""

# 询问是否要打开VSCode
$openVSCode = Read-Host "是否现在打开VSCode进行调试? (y/n)"
if ($openVSCode -eq "y" -or $openVSCode -eq "Y") {
    Write-Host "正在启动VSCode..." -ForegroundColor Yellow
    code .
}

Write-Host "设置完成！" -ForegroundColor Green
