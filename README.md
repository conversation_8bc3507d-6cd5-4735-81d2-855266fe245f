# Augment Monitor VSCode Extension

这是一个VSCode插件，用于监控Augment插件的输入输出内容，并根据预设条件自动发送响应。

## 功能特性

- 🔍 **智能监控**: 监控多种内容源，包括剪贴板、活动编辑器、文件变化等
- ⚡ **自动响应**: 根据正则表达式模式匹配自动发送预设响应
- 🎛️ **灵活配置**: 支持多个监控条件和自定义响应
- 📊 **实时监控**: 可配置的检查间隔，实时监控内容变化
- 🎯 **精准匹配**: 使用正则表达式进行精确的内容匹配

## 安装方法

1. 克隆或下载此项目
2. 在项目根目录运行：
   ```bash
   npm install
   npm run compile
   ```
3. 在VSCode中按 `F5` 启动调试模式，或者打包安装

## 使用方法

### 基本命令

- `Augment Monitor: Start` - 启动监控
- `Augment Monitor: Stop` - 停止监控  
- `Augment Monitor: Configure` - 配置监控条件

### 配置选项

在VSCode设置中可以配置以下选项：

#### `augmentMonitor.enabled`
- 类型: `boolean`
- 默认值: `false`
- 描述: 启用/禁用监控功能

#### `augmentMonitor.checkInterval`
- 类型: `number`
- 默认值: `1000`
- 描述: 检查间隔（毫秒）

#### `augmentMonitor.conditions`
- 类型: `array`
- 默认值: 
  ```json
  [
    {
      "pattern": "error|failed|exception",
      "response": "Please help me fix this error",
      "enabled": true
    }
  ]
  ```
- 描述: 监控条件和自动响应配置

### 配置示例

```json
{
  "augmentMonitor.enabled": true,
  "augmentMonitor.checkInterval": 2000,
  "augmentMonitor.conditions": [
    {
      "pattern": "error|failed|exception|错误",
      "response": "请帮我修复这个错误",
      "enabled": true
    },
    {
      "pattern": "完成|complete|done",
      "response": "谢谢！请继续下一步",
      "enabled": true
    },
    {
      "pattern": "需要更多信息|need more info",
      "response": "我来提供更多详细信息",
      "enabled": true
    }
  ]
}
```

## 监控机制

插件通过以下方式监控Augment的活动：

1. **剪贴板监控**: 检测剪贴板内容变化
2. **编辑器监控**: 监控活动编辑器的内容变化
3. **文件系统监控**: 监控工作区文件变化，特别是：
   - Markdown文件 (*.md)
   - 日志文件 (*.log)
   - Augment相关目录和文件
4. **输出通道监控**: 尝试监控相关输出通道

## 自动响应机制

当检测到的内容匹配预设的正则表达式模式时，插件会：

1. 首先尝试发送到活动终端
2. 如果没有活动终端，则将响应复制到剪贴板并显示通知

## 注意事项

- 由于VSCode API的限制，无法直接访问其他插件的webview内容
- 终端内容读取受到API限制，主要依赖其他监控方式
- 建议根据实际使用场景调整监控条件和响应

## 开发说明

### 项目结构
```
├── package.json          # 插件配置和依赖
├── tsconfig.json         # TypeScript配置
├── src/
│   └── extension.ts      # 主要插件代码
└── README.md            # 说明文档
```

### 编译和调试
```bash
# 安装依赖
npm install

# 编译TypeScript
npm run compile

# 监听模式编译
npm run watch
```

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 许可证

MIT License
