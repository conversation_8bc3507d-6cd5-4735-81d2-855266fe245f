# Augment Monitor VSCode Extension

这是一个VSCode插件，用于监控Augment插件的输入输出内容，并根据预设条件自动发送响应。

## 功能特性

- 🔍 **智能监控**: 监控多种内容源，包括剪贴板、活动编辑器、文件变化等
- ⚡ **自动响应**: 根据正则表达式模式匹配自动发送预设响应
- 🎛️ **灵活配置**: 支持多个监控条件和自定义响应
- 📊 **实时监控**: 可配置的检查间隔，实时监控内容变化
- 🎯 **精准匹配**: 使用正则表达式进行精确的内容匹配

## 快速开始

### 方法1: 使用PowerShell脚本（推荐）
```powershell
# 在项目根目录运行
.\setup.ps1
```

### 方法2: 手动安装
1. 克隆或下载此项目
2. 在项目根目录运行：
   ```bash
   npm install
   npm run compile
   ```
3. 在VSCode中按 `F5` 启动调试模式

### 方法3: 打包安装
```bash
# 安装vsce工具
npm install -g vsce

# 打包插件
vsce package

# 安装.vsix文件
code --install-extension augment-monitor-0.0.1.vsix
```

## 使用方法

### 基本命令

- `Augment Monitor: Start` - 启动监控
- `Augment Monitor: Stop` - 停止监控  
- `Augment Monitor: Configure` - 配置监控条件

### 配置选项

在VSCode设置中可以配置以下选项：

#### `augmentMonitor.enabled`
- 类型: `boolean`
- 默认值: `false`
- 描述: 启用/禁用监控功能

#### `augmentMonitor.checkInterval`
- 类型: `number`
- 默认值: `1000`
- 描述: 检查间隔（毫秒）

#### `augmentMonitor.conditions`
- 类型: `array`
- 默认值: 
  ```json
  [
    {
      "pattern": "error|failed|exception",
      "response": "Please help me fix this error",
      "enabled": true
    }
  ]
  ```
- 描述: 监控条件和自动响应配置

### 配置示例

```json
{
  "augmentMonitor.enabled": true,
  "augmentMonitor.checkInterval": 2000,
  "augmentMonitor.conditions": [
    {
      "pattern": "error|failed|exception|错误",
      "response": "请帮我修复这个错误",
      "enabled": true
    },
    {
      "pattern": "完成|complete|done",
      "response": "谢谢！请继续下一步",
      "enabled": true
    },
    {
      "pattern": "需要更多信息|need more info",
      "response": "我来提供更多详细信息",
      "enabled": true
    }
  ]
}
```

## 监控机制

插件使用VSCode API实时监控多种内容源：

### 1. 文档变化监控
- 使用 `vscode.workspace.onDidChangeTextDocument` 监控文档内容变化
- 实时检测编辑器中的内容修改
- 智能识别Augment相关内容

### 2. 编辑器状态监控
- 使用 `vscode.window.onDidChangeActiveTextEditor` 监控活动编辑器切换
- 检测选中内容和最近添加的内容
- 自动分析编辑器中的代码和文本

### 3. 窗口焦点监控
- 使用 `vscode.window.onDidChangeWindowState` 监控窗口状态
- 当窗口获得焦点时检查剪贴板内容
- 捕获从外部复制的Augment响应

### 4. Webview消息监控
- 监控webview面板的消息传递
- 尝试捕获Augment插件的界面交互
- 处理来自webview的数据更新

### 5. 定期检查机制
- 作为备用监控方式的定期检查
- 检查剪贴板、活动终端、最近文档
- 可配置的检查间隔

## 自动响应机制

当检测到的内容匹配预设的正则表达式模式时，插件会按优先级尝试以下响应方式：

### 1. 终端输入 (最高优先级)
- 如果有活动终端，直接发送响应文本
- 适用于命令行交互场景

### 2. 编辑器插入
- 在当前光标位置插入响应文本
- 适用于代码编辑和文档编写

### 3. 剪贴板 + 智能通知
- 将响应复制到剪贴板
- 显示交互式通知，提供多种操作选项：
  - **Paste Now**: 立即粘贴到当前位置
  - **Show Full Response**: 在新文档中显示完整响应
  - **Dismiss**: 忽略通知

### 4. 冷却机制
- 每个条件都有独立的冷却时间
- 避免同一条件在短时间内重复触发
- 可配置的冷却间隔（默认3-15秒）

## 测试插件

### 使用测试文件
1. 打开 `test-content.md` 文件
2. 启动Augment Monitor (`Ctrl+Shift+P` -> `Start Augment Monitor`)
3. 复制文件中的不同测试内容到剪贴板
4. 或者在编辑器中选择测试内容
5. 观察是否触发相应的自动响应

### 手动测试步骤
1. **错误测试**: 复制包含"error"、"failed"等关键词的文本
2. **完成测试**: 复制包含"完成"、"done"等关键词的文本
3. **信息请求测试**: 复制包含"需要更多信息"等关键词的文本
4. **代码测试**: 复制包含代码块的内容

### 调试信息
- 打开VSCode开发者工具 (`Help` -> `Toggle Developer Tools`)
- 查看Console标签页的日志输出
- 搜索"Augment Monitor"相关日志

## 故障排除

### 常见问题

**Q: 插件没有响应**
- 确认插件已启动 (`Start Augment Monitor`)
- 检查设置中 `augmentMonitor.enabled` 是否为 `true`
- 查看开发者工具中的错误信息

**Q: 响应触发太频繁**
- 增加条件的 `cooldown` 时间
- 调整正则表达式模式，使其更精确
- 禁用不需要的监控条件

**Q: 某些内容没有被识别**
- 检查 `looksLikeAugmentContent` 函数的识别逻辑
- 添加更多关键词到识别模式中
- 调整内容长度和结构的判断条件

**Q: 自动响应没有发送到正确位置**
- 确保有活动的终端或编辑器
- 检查剪贴板权限
- 尝试手动粘贴剪贴板内容

### 日志分析
插件会在控制台输出详细的调试信息：
```
Augment Monitor started with interval: 2000
Processing content from clipboard, length: 156
Condition matched: error|failed in clipboard
Sending auto-response for clipboard: 请帮我修复这个错误
```

## 注意事项

- VSCode API限制了对其他插件webview的直接访问
- 终端内容读取受到安全限制，主要通过其他方式监控
- 建议根据实际使用场景调整监控条件和响应
- 插件会消耗一定的系统资源，建议合理设置检查间隔

## 开发说明

### 项目结构
```
├── package.json          # 插件配置和依赖
├── tsconfig.json         # TypeScript配置
├── src/
│   └── extension.ts      # 主要插件代码
└── README.md            # 说明文档
```

### 编译和调试
```bash
# 安装依赖
npm install

# 编译TypeScript
npm run compile

# 监听模式编译
npm run watch
```

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 许可证

MIT License
