# Augment Monitor 演示

## 使用演示

### 1. 启动插件
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Start Augment Monitor"
3. 选择并执行命令
4. 看到 "Augment Monitor started" 提示

### 2. 配置插件
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Configure Augment Monitor"
3. 选择配置选项：
   - Add new condition: 添加新的监控条件
   - Edit existing conditions: 编辑现有条件
   - Toggle monitoring: 开启/关闭监控
   - Set check interval: 设置检查间隔

### 3. 测试自动响应

#### 测试错误响应
复制以下内容到剪贴板：
```
Error: Module not found
TypeError: Cannot read property 'length' of undefined
```
**预期结果**: 自动触发错误修复响应

#### 测试完成响应  
复制以下内容：
```
任务已完成！所有功能都已实现。
Task completed successfully!
```
**预期结果**: 自动触发继续操作响应

#### 测试信息请求响应
复制以下内容：
```
我需要更多信息来完成这个任务
Need more information to proceed
```
**预期结果**: 自动触发提供信息响应

### 4. 监控编辑器内容
1. 在编辑器中输入或粘贴包含关键词的内容
2. 选择包含关键词的文本
3. 观察是否触发自动响应

### 5. 查看调试信息
1. 按 `F12` 打开开发者工具
2. 切换到 Console 标签
3. 查找 "Augment Monitor" 相关日志

## 实际使用场景

### 场景1: 错误处理
当Augment返回错误信息时，自动发送修复请求：
- 检测到错误关键词
- 自动发送 "请帮我修复这个错误，需要详细的解决方案"

### 场景2: 任务完成
当Augment完成任务时，自动请求下一步：
- 检测到完成关键词  
- 自动发送 "谢谢！请继续下一步操作"

### 场景3: 信息补充
当Augment需要更多信息时，自动提供：
- 检测到信息请求关键词
- 自动发送 "我来提供更多详细信息和上下文"

### 场景4: 代码优化
当涉及代码时，自动请求优化：
- 检测到优化关键词
- 自动发送 "请帮我优化这段代码的性能"

## 高级配置

### 自定义监控条件
在VSCode设置中添加：
```json
{
  "augmentMonitor.conditions": [
    {
      "pattern": "自定义关键词|custom keyword",
      "response": "自定义响应内容",
      "enabled": true,
      "caseSensitive": false,
      "cooldown": 5000
    }
  ]
}
```

### 调整检查间隔
```json
{
  "augmentMonitor.checkInterval": 3000
}
```

### 启用/禁用监控
```json
{
  "augmentMonitor.enabled": true
}
```

## 注意事项

1. **性能影响**: 监控会消耗一定资源，建议合理设置间隔
2. **隐私考虑**: 插件会读取剪贴板和编辑器内容
3. **冷却机制**: 避免频繁触发，每个条件都有冷却时间
4. **正则表达式**: 支持复杂的模式匹配，但要注意性能

## 停止插件

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Stop Augment Monitor"
3. 选择并执行命令
4. 看到 "Augment Monitor stopped" 提示
