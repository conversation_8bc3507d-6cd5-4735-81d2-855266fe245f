{"version": 3, "file": "contentMonitor.js", "sourceRoot": "", "sources": ["../src/contentMonitor.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAgBjC,MAAa,cAAc;IAMvB;QALQ,qBAAgB,GAAwB,IAAI,GAAG,EAAE,CAAC;QAClD,iBAAY,GAA+B,EAAE,CAAC;QAC9C,yBAAoB,GAAW,EAAE,CAAC;QAClC,sBAAiB,GAAW,EAAE,CAAC;QAyI/B,sBAAiB,GAAoC,GAAG,EAAE,GAAE,CAAC,CAAC;QAtIlE,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,eAAe;QACjB,MAAM,OAAO,GAAoB,EAAE,CAAC;QAEpC,QAAQ;QACR,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QACpD,IAAI,eAAe;YAAE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEnD,UAAU;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC9C,IAAI,YAAY;YAAE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE7C,YAAY;QACZ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACxD,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAE7B,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACtD,IAAI,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,oBAAoB,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/E,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;gBAEpC,oBAAoB;gBACpB,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE;oBACvC,OAAO;wBACH,OAAO;wBACP,MAAM,EAAE,WAAW;wBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;qBACxB,CAAC;iBACL;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;SACrD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,iBAAiB;QACrB,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/B,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAEzC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACpB,kBAAkB;YAClB,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACtD;aAAM;YACH,oBAAoB;YACpB,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;YACvC,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;YAC7C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,SAAS,EAAE,CAAC,EACZ,SAAS,GAAG,CAAC,EACb,QAAQ,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAC7C,CAAC;YACF,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACrC;QAED,IAAI,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5E,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;YAEjC,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE;gBACvC,OAAO;oBACH,OAAO;oBACP,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC;aACL;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,kBAAkB;QAClB,yCAAyC;QACzC,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,iBAAiB;QACrB,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB;YAAE,OAAO;QAE9B,WAAW;QACX,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,MAAM,QAAQ,GAAG;YACb,SAAS;YACT,UAAU;YACV,kBAAkB;YAClB,cAAc;YACd,cAAc;SACjB,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACvB,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAElE,MAAM,gBAAgB,GAAG,KAAK,EAAE,GAAe,EAAE,EAAE;gBAC/C,IAAI;oBACA,YAAY;oBACZ,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACjD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI;wBAAE,OAAO,CAAC,aAAa;oBAElD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;oBAC9D,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;oBAEnC,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE;wBACvC,SAAS;wBACT,IAAI,CAAC,iBAAiB,CAAC;4BACnB,OAAO;4BACP,MAAM,EAAE,QAAQ,GAAG,CAAC,MAAM,EAAE;4BAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;yBACxB,CAAC,CAAC;qBACN;iBACJ;gBAAC,OAAO,KAAK,EAAE;oBACZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;iBACvD;YACL,CAAC,CAAC;YAEF,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACtC,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YAEtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACP,CAAC;IAID,0BAA0B,CAAC,QAAyC;QAChE,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;IACtC,CAAC;IAED,uBAAuB,CAAC,OAAe;QACnC,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,cAAc;QACd,MAAM,iBAAiB,GAAG;YACtB,SAAS;YACT,QAAQ;YACR,WAAW;YACX,aAAa;YACb,gBAAgB;YAChB,cAAc;YACd,sBAAsB;YACtB,MAAM;YACN,OAAO;YACP,oBAAoB;YACpB,oBAAoB;SACvB,CAAC;QAEF,OAAO;QACP,MAAM,cAAc,GAAG;YACnB,KAAK;YACL,UAAU;YACV,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;SACT,CAAC;QAEF,kBAAkB;QAClB,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAC1D,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CACnC,CAAC;QAEF,uBAAuB;QACvB,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CACpD,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CACnC,CAAC;QAEF,UAAU;QACV,MAAM,mBAAmB,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;QAC1E,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAExD,OAAO,kBAAkB,IAAI,CAAC,eAAe,IAAI,mBAAmB,IAAI,gBAAgB,CAAC,CAAC;IAC9F,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAwB,EAAE,UAA8B;QAC5E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAChC,IAAI,CAAC,SAAS,CAAC,OAAO;oBAAE,SAAS;gBAEjC,SAAS;gBACT,MAAM,YAAY,GAAG,GAAG,SAAS,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACjE,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,SAAS;gBAEtD,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,GAAG,QAAQ,EAAE;oBACrC,SAAS;iBACZ;gBAED,SAAS;gBACT,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;gBACnD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAEnD,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;oBAC5B,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,OAAO,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAE3E,SAAS;oBACT,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;oBAEpD,OAAO;oBACP,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBAEpD,cAAc;oBACd,MAAM;iBACT;aACJ;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAsB;QAC/D,IAAI;YACA,iBAAiB;YACjB,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YACpD,IAAI,cAAc,EAAE;gBAChB,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAClC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,mCAAmC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CACpE,CAAC;gBACF,OAAO;aACV;YAED,iBAAiB;YACjB,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACrD,wBAAwB,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,EACtD,kBAAkB,EAClB,SAAS,CACZ,CAAC;YAEF,IAAI,MAAM,KAAK,kBAAkB,EAAE;gBAC/B,aAAa;gBACb,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBACpD,IAAI,YAAY,EAAE;oBACd,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC/C,MAAM,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;wBAClC,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC;iBACN;aACJ;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;SAC5E;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3B,CAAC;CACJ;AA9QD,wCA8QC"}