# Augment Monitor 测试内容

这个文件包含了一些测试内容，用于验证Augment Monitor插件的功能。

## 错误测试内容

这里有一个错误信息：
```
Error: Cannot find module 'express'
    at Function.Module._resolveFilename (internal/modules/cjs/loader.js:636:15)
    at Function.Module._load (internal/modules/cjs/loader.js:562:25)
```

这应该触发错误相关的自动响应。

## 完成测试内容

任务已经完成！所有的功能都已经实现。

这应该触发完成相关的自动响应。

## 需要更多信息测试

我需要更多信息来完成这个任务，请提供更多详细信息。

这应该触发需要更多信息的自动响应。

## 测试相关内容

请帮我编写单元测试来验证这个功能。

这应该触发测试相关的自动响应。

## Augment相关内容

这是一个来自Augment Assistant的响应：

```javascript
function example() {
    console.log("Hello from Augment!");
}
```

这应该被识别为Augment内容。

## 代码示例

```python
def hello_world():
    print("Hello, World!")
    return "success"
```

这是一个Python代码示例，应该被识别为代码内容。

## 使用说明

1. 启动Augment Monitor
2. 复制上述任何一段内容到剪贴板
3. 或者在编辑器中选择任何一段内容
4. 观察是否触发自动响应

## 测试步骤

1. 确保插件已启动
2. 逐个测试不同类型的内容
3. 检查控制台输出
4. 验证自动响应是否正确触发

## 预期行为

- 错误内容应该触发错误修复响应
- 完成内容应该触发继续操作响应  
- 信息请求应该触发提供信息响应
- 测试内容应该触发测试编写响应（如果启用）
