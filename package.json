{"name": "augment-monitor", "displayName": "Augment Monitor", "description": "Monitor Augment plugin input/output and auto-respond based on conditions", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augment-monitor.start", "title": "Start Augment Monitor"}, {"command": "augment-monitor.stop", "title": "Stop Augment Monitor"}, {"command": "augment-monitor.configure", "title": "Configure Augment Monitor"}], "configuration": {"title": "Augment Monitor", "properties": {"augmentMonitor.checkInterval": {"type": "number", "default": 1000, "description": "Interval in milliseconds to check Augment output"}, "augmentMonitor.conditions": {"type": "array", "default": [{"pattern": "error|failed|exception", "response": "Please help me fix this error", "enabled": true}], "description": "Conditions and responses for auto-input"}, "augmentMonitor.enabled": {"type": "boolean", "default": false, "description": "Enable/disable the monitor"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}