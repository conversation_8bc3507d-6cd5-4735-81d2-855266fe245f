{"name": "augment-monitor", "displayName": "Augment Monitor", "description": "Monitor Augment plugin input/output and auto-respond based on conditions", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augment-monitor.start", "title": "Start Augment Monitor"}, {"command": "augment-monitor.stop", "title": "Stop Augment Monitor"}, {"command": "augment-monitor.configure", "title": "Configure Augment Monitor"}], "configuration": {"title": "Augment Monitor", "properties": {"augmentMonitor.checkInterval": {"type": "number", "default": 1000, "description": "Interval in milliseconds to check Augment output"}, "augmentMonitor.conditions": {"type": "array", "default": [{"pattern": "error|failed|exception|错误|失败", "response": "请帮我修复这个错误，需要详细的解决方案", "enabled": true, "caseSensitive": false, "cooldown": 5000}, {"pattern": "完成|complete|done|finished", "response": "谢谢！请继续下一步操作", "enabled": true, "caseSensitive": false, "cooldown": 3000}, {"pattern": "需要更多信息|need more info|clarification needed", "response": "我来提供更多详细信息和上下文", "enabled": true, "caseSensitive": false, "cooldown": 5000}, {"pattern": "测试|test|testing", "response": "请帮我编写相应的测试用例", "enabled": false, "caseSensitive": false, "cooldown": 10000}], "description": "Conditions and responses for auto-input"}, "augmentMonitor.enabled": {"type": "boolean", "default": false, "description": "Enable/disable the monitor"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}