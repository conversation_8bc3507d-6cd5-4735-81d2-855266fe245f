import * as vscode from 'vscode';

interface MonitorCondition {
    pattern: string;
    response: string;
    enabled: boolean;
}

export function activate(context: vscode.ExtensionContext) {
    console.log('Augment Monitor is now active!');

    const monitor = new AugmentMonitor();

    // Register commands
    const startCommand = vscode.commands.registerCommand('augment-monitor.start', () => {
        monitor.start();
        vscode.window.showInformationMessage('Augment Monitor started');
    });

    const stopCommand = vscode.commands.registerCommand('augment-monitor.stop', () => {
        monitor.stop();
        vscode.window.showInformationMessage('Augment Monitor stopped');
    });

    const configureCommand = vscode.commands.registerCommand('augment-monitor.configure', () => {
        monitor.configure();
    });

    context.subscriptions.push(startCommand, stopCommand, configureCommand);

    // Auto-start if enabled in settings
    const config = vscode.workspace.getConfiguration('augmentMonitor');
    if (config.get('enabled')) {
        monitor.start();
    }
}

class AugmentMonitor {
    private intervalId: NodeJS.Timeout | null = null;
    private lastOutputContent: string = '';
    private augmentOutputChannel: vscode.OutputChannel | null = null;

    constructor() {
        this.findAugmentOutputChannel();
    }

    private findAugmentOutputChannel() {
        // Try to find Augment's output channel
        // This is a simplified approach - in reality, we might need to use VSCode API
        // to access other extension's output channels or webview content
        try {
            // Look for common Augment output channel names
            const possibleNames = ['Augment', 'Augment Agent', 'Augment Code'];
            
            // Note: VSCode doesn't provide direct access to other extensions' output channels
            // This is a limitation we'll need to work around
            console.log('Attempting to find Augment output channel...');
        } catch (error) {
            console.error('Error finding Augment output channel:', error);
        }
    }

    start() {
        if (this.intervalId) {
            return; // Already running
        }

        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const interval = config.get<number>('checkInterval', 1000);

        this.intervalId = setInterval(() => {
            this.checkAugmentOutput();
        }, interval);

        console.log('Augment Monitor started with interval:', interval);
    }

    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('Augment Monitor stopped');
        }
    }

    private async checkAugmentOutput() {
        try {
            // Method 1: Try to access active terminal content
            const activeTerminal = vscode.window.activeTerminal;
            if (activeTerminal && activeTerminal.name.toLowerCase().includes('augment')) {
                // Unfortunately, VSCode doesn't provide direct access to terminal content
                console.log('Found potential Augment terminal');
            }

            // Method 2: Try to access webview content (for Augment's chat interface)
            await this.checkWebviewContent();

            // Method 3: Monitor file changes that might indicate Augment activity
            await this.monitorFileChanges();

        } catch (error) {
            console.error('Error checking Augment output:', error);
        }
    }

    private async checkWebviewContent() {
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get<MonitorCondition[]>('conditions', []);

        // Method 1: Monitor clipboard changes (common way Augment content is copied)
        const clipboardContent = await this.getClipboardContent();

        // Method 2: Monitor active editor changes
        const editorContent = this.getActiveEditorContent();

        // Method 3: Monitor output channels
        const outputContent = await this.getOutputChannelContent();

        // Method 4: Monitor terminal content (if accessible)
        const terminalContent = await this.getTerminalContent();

        const allContent = [clipboardContent, editorContent, outputContent, terminalContent]
            .filter(content => content && content.trim().length > 0)
            .join('\n');

        if (allContent !== this.lastOutputContent && allContent.trim().length > 0) {
            this.lastOutputContent = allContent;
            await this.processContent(allContent, conditions);
        }
    }

    private async getClipboardContent(): Promise<string> {
        try {
            return await vscode.env.clipboard.readText();
        } catch (error) {
            return '';
        }
    }

    private getActiveEditorContent(): string {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            const selection = activeEditor.selection;
            if (!selection.isEmpty) {
                return activeEditor.document.getText(selection);
            }
            // Return last few lines of the document to catch recent additions
            const document = activeEditor.document;
            const lineCount = document.lineCount;
            const startLine = Math.max(0, lineCount - 10);
            const range = new vscode.Range(startLine, 0, lineCount - 1, document.lineAt(lineCount - 1).text.length);
            return document.getText(range);
        }
        return '';
    }

    private async getOutputChannelContent(): Promise<string> {
        // Try to find and read from output channels that might contain Augment content
        // This is limited by VSCode API - we can only access our own output channels directly
        try {
            if (!this.augmentOutputChannel) {
                this.augmentOutputChannel = vscode.window.createOutputChannel('Augment Monitor Log');
            }
            // Log what we're monitoring for debugging
            return '';
        } catch (error) {
            return '';
        }
    }

    private async getTerminalContent(): Promise<string> {
        // VSCode doesn't provide direct access to terminal content
        // This is a limitation of the current API
        const activeTerminal = vscode.window.activeTerminal;
        if (activeTerminal) {
            // We can only send text to terminals, not read from them directly
            // Consider using a workaround like having the terminal output to a file
            return '';
        }
        return '';
    }

    private async processContent(content: string, conditions: MonitorCondition[]) {
        for (const condition of conditions) {
            if (!condition.enabled) continue;

            const regex = new RegExp(condition.pattern, 'i');
            if (regex.test(content)) {
                console.log(`Condition matched: ${condition.pattern}`);
                await this.sendResponseToAugment(condition.response);
                break; // Only respond to first matching condition
            }
        }
    }

    private async sendResponseToAugment(response: string) {
        try {
            // Method 1: Try to send to active terminal
            const activeTerminal = vscode.window.activeTerminal;
            if (activeTerminal) {
                activeTerminal.sendText(response);
                console.log(`Sent response to terminal: ${response}`);
                return;
            }

            // Method 2: Copy to clipboard and notify user
            await vscode.env.clipboard.writeText(response);
            vscode.window.showInformationMessage(
                `Auto-response copied to clipboard: ${response}`,
                'Paste to Augment'
            );

        } catch (error) {
            console.error('Error sending response to Augment:', error);
        }
    }

    private async monitorFileChanges() {
        // Monitor workspace for file changes that might indicate Augment activity
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) return;

        // Watch for file changes in common Augment output locations
        const patterns = [
            '**/*.md',  // Markdown files often used by Augment
            '**/*.log', // Log files
            '**/.augment/**', // Augment-specific directories
            '**/augment-*', // Files with augment prefix
        ];

        for (const pattern of patterns) {
            const watcher = vscode.workspace.createFileSystemWatcher(pattern);

            watcher.onDidChange(async (uri) => {
                try {
                    const document = await vscode.workspace.openTextDocument(uri);
                    const content = document.getText();

                    // Check if this looks like Augment output
                    if (this.looksLikeAugmentContent(content)) {
                        const config = vscode.workspace.getConfiguration('augmentMonitor');
                        const conditions = config.get<MonitorCondition[]>('conditions', []);
                        await this.processContent(content, conditions);
                    }
                } catch (error) {
                    console.error('Error reading changed file:', error);
                }
            });

            watcher.onDidCreate(async (uri) => {
                // Similar logic for newly created files
                try {
                    const document = await vscode.workspace.openTextDocument(uri);
                    const content = document.getText();

                    if (this.looksLikeAugmentContent(content)) {
                        const config = vscode.workspace.getConfiguration('augmentMonitor');
                        const conditions = config.get<MonitorCondition[]>('conditions', []);
                        await this.processContent(content, conditions);
                    }
                } catch (error) {
                    console.error('Error reading new file:', error);
                }
            });
        }
    }

    private looksLikeAugmentContent(content: string): boolean {
        // Heuristics to determine if content might be from Augment
        const augmentIndicators = [
            'augment',
            'claude',
            'assistant',
            'ai response',
            'generated code',
            'function_calls',
            'antml:invoke'
        ];

        const lowerContent = content.toLowerCase();
        return augmentIndicators.some(indicator => lowerContent.includes(indicator));
    }

    async configure() {
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get<MonitorCondition[]>('conditions', []);

        const options = [
            'Add new condition',
            'Edit existing conditions',
            'Toggle monitoring',
            'Set check interval'
        ];

        const selection = await vscode.window.showQuickPick(options, {
            placeHolder: 'Choose configuration option'
        });

        switch (selection) {
            case 'Add new condition':
                await this.addNewCondition();
                break;
            case 'Edit existing conditions':
                await this.editConditions();
                break;
            case 'Toggle monitoring':
                await this.toggleMonitoring();
                break;
            case 'Set check interval':
                await this.setCheckInterval();
                break;
        }
    }

    private async addNewCondition() {
        const pattern = await vscode.window.showInputBox({
            prompt: 'Enter regex pattern to match',
            placeholder: 'e.g., error|failed|exception'
        });

        if (!pattern) return;

        const response = await vscode.window.showInputBox({
            prompt: 'Enter response to send when pattern matches',
            placeholder: 'e.g., Please help me fix this error'
        });

        if (!response) return;

        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get<MonitorCondition[]>('conditions', []);
        
        conditions.push({
            pattern,
            response,
            enabled: true
        });

        await config.update('conditions', conditions, vscode.ConfigurationTarget.Global);
        vscode.window.showInformationMessage('New condition added successfully');
    }

    private async editConditions() {
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const conditions = config.get<MonitorCondition[]>('conditions', []);

        if (conditions.length === 0) {
            vscode.window.showInformationMessage('No conditions configured');
            return;
        }

        const items = conditions.map((condition, index) => ({
            label: condition.pattern,
            description: condition.response,
            detail: condition.enabled ? 'Enabled' : 'Disabled',
            index
        }));

        const selection = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select condition to edit'
        });

        if (selection) {
            // Toggle enabled state for now - could be expanded to full editing
            conditions[selection.index].enabled = !conditions[selection.index].enabled;
            await config.update('conditions', conditions, vscode.ConfigurationTarget.Global);
            vscode.window.showInformationMessage(
                `Condition ${conditions[selection.index].enabled ? 'enabled' : 'disabled'}`
            );
        }
    }

    private async toggleMonitoring() {
        const config = vscode.workspace.getConfiguration('augmentMonitor');
        const enabled = config.get<boolean>('enabled', false);
        
        await config.update('enabled', !enabled, vscode.ConfigurationTarget.Global);
        
        if (!enabled) {
            this.start();
        } else {
            this.stop();
        }

        vscode.window.showInformationMessage(
            `Monitoring ${!enabled ? 'enabled' : 'disabled'}`
        );
    }

    private async setCheckInterval() {
        const input = await vscode.window.showInputBox({
            prompt: 'Enter check interval in milliseconds',
            placeholder: '1000',
            validateInput: (value) => {
                const num = parseInt(value);
                if (isNaN(num) || num < 100) {
                    return 'Please enter a valid number >= 100';
                }
                return null;
            }
        });

        if (input) {
            const config = vscode.workspace.getConfiguration('augmentMonitor');
            await config.update('checkInterval', parseInt(input), vscode.ConfigurationTarget.Global);
            vscode.window.showInformationMessage(`Check interval set to ${input}ms`);
            
            // Restart monitoring with new interval
            if (this.intervalId) {
                this.stop();
                this.start();
            }
        }
    }
}

export function deactivate() {
    console.log('Augment Monitor deactivated');
}
